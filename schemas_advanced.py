from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union
from enum import Enum

# ====== 🧠 基于认知科学的激进创新设计 ======

class CognitiveStrategy(str, Enum):
    """认知策略枚举"""
    PATTERN_RECOGNITION = "pattern_recognition"  # 模式识别
    ANALOGICAL_REASONING = "analogical_reasoning"  # 类比推理
    CAUSAL_INFERENCE = "causal_inference"  # 因果推理
    METACOGNITIVE_MONITORING = "metacognitive_monitoring"  # 元认知监控

class LearningObjective(str, Enum):
    """学习目标枚举"""
    BOUNDARY_DETECTION = "boundary_detection"
    TYPE_CLASSIFICATION = "type_classification"  
    CONTEXT_INTEGRATION = "context_integration"
    AMBIGUITY_RESOLUTION = "ambiguity_resolution"
    DOMAIN_ADAPTATION = "domain_adaptation"

class MetaCognitiveExampleAgent(BaseModel):
    """
    🚀 元认知智能体 - 基于认知科学的NER示例需求分析
    
    核心创新：
    1. 多层次认知建模：从感知到元认知的完整建模
    2. 自适应推理策略：根据任务动态调整认知策略  
    3. 质量导向检索：基于学习效果而非相似性检索
    4. 元学习能力：学会如何学习NER任务
    """
    
    # ===== 第一层：感知认知分析 =====
    perceptual_analysis: Dict[str, Any] = Field(
        description="""感知层面分析 - 让LLM模拟人类专家的初始感知:
        {
            "text_complexity": {
                "lexical_difficulty": "simple/moderate/complex/expert",
                "syntactic_complexity": "linear/nested/recursive/mixed",
                "semantic_density": "sparse/normal/dense/overwhelming"
            },
            "entity_landscape": {
                "entity_density": "low/medium/high/extreme",
                "boundary_clarity": "clear/ambiguous/overlapping/nested",
                "type_diversity": "homogeneous/mixed/heterogeneous/chaotic"
            },
            "contextual_cues": {
                "local_signals": ["punctuation", "capitalization", "formatting", "position"],
                "global_patterns": ["domain_conventions", "document_structure", "narrative_flow"],
                "implicit_knowledge": ["cultural_context", "temporal_context", "domain_expertise"]
            }
        }
        
        思考：作为NER专家，我第一眼看到这个文本时注意到了什么？"""
    )
    
    # ===== 第二层：概念认知推理 =====
    conceptual_reasoning: Dict[str, Any] = Field(
        description="""概念层面推理 - 深度理解任务本质:
        {
            "task_decomposition": {
                "primary_challenge": "boundary_detection/type_classification/context_understanding/domain_adaptation",
                "secondary_challenges": ["ambiguity_resolution", "nested_entities", "domain_transfer"],
                "cognitive_bottlenecks": ["attention_allocation", "memory_retrieval", "pattern_matching"]
            },
            "learning_requirements": {
                "prototype_examples": "需要什么样的典型示例来建立概念原型？",
                "contrastive_examples": "需要什么样的对比示例来区分边界？",
                "progressive_examples": "需要什么样的递进示例来建立理解层次？",
                "error_prevention": "需要什么样的反例来预防常见错误？"
            },
            "reasoning_strategy": {
                "primary_strategy": "pattern_matching/rule_induction/case_based/hybrid",
                "fallback_strategies": ["analogical_reasoning", "causal_inference", "elimination"],
                "confidence_indicators": ["consistency_check", "cross_validation", "domain_knowledge"]
            }
        }
        
        思考：要掌握这个NER任务，我的大脑需要进行什么样的认知过程？"""
    )
    
    # ===== 第三层：元认知监控 =====
    metacognitive_monitoring: Dict[str, Any] = Field(
        description="""元认知监控 - 监控和调节学习过程:
        {
            "self_assessment": {
                "current_confidence": "low/medium/high/overconfident",
                "knowledge_gaps": ["conceptual_gaps", "procedural_gaps", "conditional_gaps"],
                "error_patterns": ["false_positives", "false_negatives", "boundary_errors", "type_confusion"],
                "metacognitive_awareness": "我知道我不知道什么"
            },
            "strategy_selection": {
                "current_strategy_effectiveness": "ineffective/partially_effective/effective/optimal",
                "strategy_adjustment_needed": "none/minor/major/complete_change",
                "alternative_strategies": ["change_attention_focus", "seek_different_examples", "adjust_criteria"],
                "learning_path_optimization": "如何优化学习路径？"
            },
            "adaptive_feedback": {
                "example_quality_assessment": "当前示例质量如何？",
                "learning_progress_evaluation": "学习进展如何？",
                "next_step_planning": "下一步应该寻找什么样的示例？",
                "termination_criteria": "什么时候停止寻找更多示例？"
            }
        }
        
        思考：我如何监控和调节自己的NER学习过程？"""
    )
    
    # ===== 第四层：质量导向检索策略 =====
    quality_driven_retrieval: Dict[str, Any] = Field(
        description="""质量导向检索策略 - 基于学习效果而非相似性:
        {
            "example_utility_function": {
                "informativeness": 0.0-1.0,  # 信息价值：能提供多少新信息
                "discriminativeness": 0.0-1.0,  # 区分度：能帮助区分不同类别
                "transferability": 0.0-1.0,  # 迁移性：能泛化到类似情况
                "memorability": 0.0-1.0,  # 记忆性：容易记住和应用
                "difficulty_appropriateness": 0.0-1.0  # 难度适宜性：符合当前学习阶段
            },
            "retrieval_objectives": {
                "concept_formation": "形成清晰的实体概念",
                "boundary_learning": "学习准确的边界识别",
                "context_sensitivity": "培养上下文敏感性", 
                "error_prevention": "预防典型错误",
                "confidence_calibration": "校准预测置信度"
            },
            "dynamic_adaptation": {
                "performance_monitoring": "实时监控检索效果",
                "strategy_switching": "根据效果动态切换策略",
                "example_set_evolution": "示例集合的演化优化",
                "termination_optimization": "最优停止时机判断"
            }
        }
        
        思考：什么样的示例能最大化我的NER学习效果？"""
    )
    
    # ===== 第五层：智能体自我反思 =====
    agent_self_reflection: Dict[str, Any] = Field(
        description="""智能体自我反思 - 元元认知层面:
        {
            "analysis_completeness": {
                "covered_aspects": "我分析了哪些方面？",
                "missing_perspectives": "我遗漏了哪些重要视角？",
                "depth_assessment": "分析深度是否足够？",
                "breadth_assessment": "分析广度是否全面？"
            },
            "reasoning_quality": {
                "logical_consistency": "我的推理逻辑是否一致？",
                "evidence_sufficiency": "证据是否充分？",
                "assumption_validity": "假设是否合理？",
                "conclusion_soundness": "结论是否可靠？"
            },
            "improvement_opportunities": {
                "knowledge_enhancement": "需要补充什么知识？",
                "method_refinement": "方法需要如何改进？",
                "perspective_expansion": "需要拓展什么视角？",
                "bias_correction": "需要纠正什么偏见？"
            },
            "meta_meta_cognition": {
                "self_model_accuracy": "我对自己能力的评估准确吗？",
                "learning_efficiency": "我的学习效率如何？",
                "adaptation_capability": "我的适应能力如何？",
                "future_learning_potential": "我还能在哪些方面提升？"
            }
        }
        
        思考：作为一个AI智能体，我如何反思和改进自己的NER分析能力？"""
    )


class AdaptiveRetrievalOrchestrator(BaseModel):
    """
    🎯 自适应检索编排器 - 基于认知分析结果的智能检索
    """
    
    retrieval_plan: Dict[str, Any] = Field(
        description="""检索计划 - 基于认知分析制定检索策略:
        {
            "phase_1_exploration": {
                "objective": "initial_understanding",
                "strategy": "diverse_sampling",
                "target_examples": 5-10,
                "quality_threshold": 0.7
            },
            "phase_2_focused_learning": {
                "objective": "concept_refinement", 
                "strategy": "targeted_retrieval",
                "target_examples": 10-20,
                "quality_threshold": 0.8
            },
            "phase_3_boundary_testing": {
                "objective": "boundary_exploration",
                "strategy": "edge_case_mining",
                "target_examples": 5-15,
                "quality_threshold": 0.9
            },
            "phase_4_validation": {
                "objective": "confidence_validation",
                "strategy": "diverse_validation",
                "target_examples": 5-10,
                "quality_threshold": 0.85
            }
        }
        
        基于认知分析，制定分阶段的检索计划"""
    )
    
    quality_assessment_criteria: List[str] = Field(
        description="""质量评估标准 - 如何评估检索到的示例质量:
        选择适用的评估标准：
        ["semantic_relevance", "structural_similarity", "difficulty_match", 
         "informativeness", "discriminativeness", "transferability",
         "error_prevention_value", "concept_clarity", "boundary_definition",
         "context_richness", "domain_representativeness"]"""
    )
    
    adaptive_mechanisms: Dict[str, str] = Field(
        description="""自适应机制 - 如何根据反馈调整检索策略:
        {
            "performance_feedback": "根据实际效果调整检索参数",
            "cognitive_load_management": "根据认知负荷调整示例复杂度",
            "learning_curve_optimization": "根据学习曲线优化示例序列",
            "confidence_calibration": "根据置信度校准检索门槛",
            "error_pattern_adaptation": "根据错误模式调整检索焦点"
        }"""
    )


# ===== 使用示例 =====
class ExampleUsage(BaseModel):
    """使用示例和最佳实践"""
    
    usage_pattern: str = Field(
        description="""推荐使用模式:
        1. 首先使用 MetaCognitiveExampleAgent 进行深度认知分析
        2. 基于分析结果使用 AdaptiveRetrievalOrchestrator 制定检索计划  
        3. 执行多阶段自适应检索
        4. 持续监控和优化检索效果
        5. 形成个性化的认知-检索闭环"""
    )
    
    integration_strategy: str = Field(
        description="""与现有系统集成策略:
        - 渐进式替换：逐步替换现有的简单描述方式
        - 并行验证：新旧方法并行运行，验证效果提升
        - 自适应融合：根据任务特点选择最适合的分析深度
        - 持续进化：基于使用反馈不断优化认知模型"""
    )