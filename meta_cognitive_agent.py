#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 元认知智能体 - 基于单一超级Prompt的NER系统
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import logging
from typing import Dict, List, Any, Type
from pydantic import BaseModel

from config import get_current_dataset_info
from model_interface import model_service
from schemas import SpecifyExampleNeedsTool
from utils import parse_tool_arguments, robust_json_parse_ner

logger = logging.getLogger(__name__)


class MetaCognitiveAgent:
    """🧠 元认知智能体 - 单一超级Prompt架构的核心引擎"""
    
    def __init__(self, example_retriever=None):
        """
        初始化元认知智能体

        Args:
            example_retriever: 预初始化的示例检索器
        """
        if example_retriever is None:
            from example_retriever import example_retriever as default_retriever
            self.example_retriever = default_retriever
        else:
            self.example_retriever = example_retriever
        self.model_service = model_service
        self._initialization_started = False
        
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _get_current_label_prompt(self) -> str:
        """获取当前数据集的标签提示"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('label_prompt', '')



    async def _ensure_initialized(self):
        """确保示例检索器已初始化"""
        if not self._initialization_started and self.example_retriever and not self.example_retriever.initialized:
            self._initialization_started = True
            logger.info("🔧 自动初始化示例检索器...")
            await self.example_retriever.initialize_vector_store()



    def _build_metacognitive_prompt(self, text: str) -> str:
        """
        {{ AURA-X: Enhance - 构建增强的元认知prompt，引导三维度分析. Approval: 寸止(ID:1738230404). }}
        构建新的元认知prompt - 引导LLM进行语义、句法、实体三维度分析
        """
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        return f"""You are a top-tier Named Entity Recognition (NER) expert. Analyze the text below and think about what specific reference examples would help you complete this task most effectively.

Target entity types: {entity_types_str}
Text to analyze: "{text}"

Think like an expert who needs specific examples to guide your recognition. Use the SpecifyExampleNeedsTool to express your needs:

1. **Semantic Analysis**: What SPECIFIC TYPES OF EXAMPLES do you need? Don't just identify the domain - think about what patterns, contexts, or situations would help you recognize entities better in this text.
   - Example: "I need examples with person names appearing after titles" (not "medical domain texts")

2. **Syntactic Analysis**: What sentence complexity level should the examples have to match this text?
   - 'simple': Basic sentences with clear structure (1-2 dependency levels)
   - 'medium': Moderate complexity with some clauses (2-4 levels)
   - 'complex': Complex sentences with deep nesting (4+ levels)

3. **Entity Analysis**: What entity distribution pattern should the examples have?
   - 'sparse': Few entities, spread apart (0-1 per sentence)
   - 'medium': Moderate density (1-3 per sentence)
   - 'dense': Many entities close together (3+ per sentence)

Focus on expressing what you NEED in examples, not what you OBSERVE in the text. Think: "What examples would make me better at recognizing entities in this specific situation?"

Use the tool to clearly express your specific example requirements."""

    def build_stage1_prompt(self, text: str) -> str:
        """
        构建Stage 1的元认知prompt
        让LLM通过自然语言描述需求
        """
        return self._build_metacognitive_prompt(text)

    async def simple_retrieval(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        公共方法：简化的检索方法
        供外部调用，避免直接访问私有方法
        """
        return await self._simple_retrieval(description, k)

    async def execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """
        公共方法：执行NER阶段
        供外部调用，避免直接访问私有方法
        """
        return await self._execute_ner_stage(text, few_shot_examples)





    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        {{ AURA-X: Add - 基于需求描述的两阶段NER流程. Approval: 寸止(ID:1738230404). }}
        新的两阶段NER流程，使用SpecifyExampleNeedsTool

        Stage 1: LLM分析文本并描述需求
        Stage 2: 基于需求描述检索示例并进行NER
        """
        try:
            await self._ensure_initialized()

            logger.info(f"🧠 Stage 1 (需求描述): 分析文本并描述示例需求: '{text[:50]}...'")

            # Stage 1: LLM分析文本并描述需求
            stage1_prompt = self._build_metacognitive_prompt(text)
            tools: List[Type[BaseModel]] = [SpecifyExampleNeedsTool]  # 使用新的需求描述工具
            messages = [{"role": "user", "content": stage1_prompt}]

            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                # 执行基于需求的检索获取few-shot
                few_shot_examples = await self._execute_needs_based_retrieval(response.tool_calls)

                if few_shot_examples:
                    logger.info(f"🧠 Stage 2: 基于{len(few_shot_examples)}个示例进行NER")
                    # Stage 2: 基于few-shot进行NER
                    return await self._execute_ner_stage(text, few_shot_examples)
                else:
                    logger.warning("❌ 未获取到few-shot示例")
                    return {}
            else:
                logger.warning("❌ LLM未调用需求描述工具")
                return {}

        except Exception as e:
            logger.error(f"基于需求的两阶段NER失败: {e}")
            return {}

    async def _execute_needs_based_retrieval(self, tool_calls: List[Any]) -> List[Any]:
        """
        {{ AURA-X: Add - 执行基于需求描述的检索阶段. Approval: 寸止(ID:1738230404). }}
        处理SpecifyExampleNeedsTool的调用，转换为多维度检索
        """
        for tool_call in tool_calls:
            if not tool_call.function or tool_call.function.name != "SpecifyExampleNeedsTool":
                continue

            try:
                # 解析需求描述参数
                arguments = parse_tool_arguments(tool_call.function.arguments)
                if arguments is None:
                    logger.warning("需求描述参数解析失败，跳过此样本")
                    return []

                # 提取四个维度的需求描述
                needs = {
                    "semantic_need": arguments.get("semantic_need", ""),
                    "syntactic_complexity": arguments.get("syntactic_complexity", ""),
                    "entity_density": arguments.get("entity_density", "")
                }

                logger.info(f"🧠 需求描述分析: {needs}")

                # 检查example_retriever是否支持基于需求的检索
                if hasattr(self.example_retriever, 'retrieve_with_needs'):
                    # 使用新的基于需求的检索方法
                    examples = await self.example_retriever.retrieve_with_needs(needs, k=3)
                else:
                    # 回退到传统检索方法，使用semantic_need作为描述
                    logger.info("🔄 回退到传统检索方法")
                    examples = await self.example_retriever.retrieve_with_dimensions(
                        query_text=needs["semantic_need"],
                        query_dimensions=None,
                        k=3
                    )

                logger.info(f"🔍 基于需求检索完成，返回{len(examples)}个示例")
                return examples

            except Exception as e:
                logger.error(f"基于需求的检索阶段失败: {e}")
                return []

        return []



    async def _execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """
        {{ AURA-X: Optimize - 强化prompt和解析. Approval: 寸止(ID:1738230400). }}
        执行Stage 2的NER阶段 - 强化prompt和多重解析
        """
        examples_text = self._format_examples_for_context(few_shot_examples)
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        ner_prompt = f"""You are an expert Named Entity Recognition system.

Extract named entities from text using ONLY the entity types specified below.

OUTPUT FORMAT REQUIREMENTS:
1. Return ONLY a valid JSON object
2. Keys must be entity types from the label set
3. Values must be arrays of entity strings
4. If no entities found for a type, use empty array []
5. If no entities found at all, return {{}}
6. NO explanations, NO additional text, ONLY JSON

Label set: {entity_types_str}

Examples (learn from these patterns):
{examples_text}

Text to analyze: "{text}"

JSON output:"""

        messages = [{"role": "user", "content": ner_prompt}]

        response = await self.model_service.generate_simple_async(
            messages=messages,
            temperature=0.0  # 使用0温度确保一致性
        )

        if response:
            entity_types = self._get_current_entity_types()
            return robust_json_parse_ner(response, entity_types)
        else:
            logger.warning("❌ Stage 2 NER失败")
            return {}

    async def _simple_retrieval(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        🔍 简化的检索方法 - 直接基于description检索k个示例
        """
        try:
            if not self.example_retriever or not self.example_retriever.initialized:
                logger.warning("⚠️ 示例检索器未初始化")
                return []

            # 直接使用description和k进行检索
            examples = await self.example_retriever.simple_retrieve(description, k)
            return examples

        except Exception as e:
            logger.error(f"简化检索失败: {e}")
            return []

    def _format_examples_for_context(self, examples) -> str:
        """
        {{ AURA-X: Optimize - 内联数据提取逻辑，减少函数调用. Approval: 寸止(ID:1738230400). }}
        优化的示例格式化方法
        """
        if not examples:
            return "No examples available."

        formatted_examples = []
        for i, example in enumerate(examples, 1):
            # 提取示例数据
            if hasattr(example, 'example'):
                example_data = example.example
            elif isinstance(example, dict):
                example_data = example
            else:
                example_data = {}
            if not example_data:
                continue

            text = example_data.get('text', '')
            labels = example_data.get('label', {})

            # 简化的实体格式化
            entities_str = self._format_entities(labels)
            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")

        return "\n\n".join(formatted_examples)



    def _format_entities(self, labels: Dict[str, List[str]]) -> str:
        """
        简化的实体格式化
        """
        entities = []
        for etype, entities_list in labels.items():
            for entity in entities_list:
                entities.append(f"'{entity}' ({etype})")
        return ", ".join(entities)




# 🚀 全局实例管理
_meta_cognitive_agent = None

def get_meta_cognitive_agent(example_retriever=None):
    """获取元认知智能体实例"""
    global _meta_cognitive_agent
    if _meta_cognitive_agent is None:
        _meta_cognitive_agent = MetaCognitiveAgent(example_retriever)
    return _meta_cognitive_agent
