from pydantic import BaseModel, Field

# ====== 🚀 元认知智能体Function Calling工具 ======


class SpecifyExampleNeedsTool(BaseModel):
    """🧠 元认知驱动的NER示例需求描述工具 - 基于语义和结构特征的智能检索"""

    semantic_need: str = Field(
        description="""Describe what specific types of examples you need to help with entity recognition in the current text. 
        Focus on WHAT KIND OF EXAMPLES would help, not what domain the text belongs to.
        
        Think about:
        - What specific patterns or contexts would help you identify entities better?
        - What similar situations have you seen that would guide your recognition?
        - What examples would clarify entity boundaries or disambiguation?

        Good examples:
        - 'I need examples with person names appearing after titles like Dr. or Prof.'
        - 'I need examples with organization names that contain numbers or abbreviations'
        - 'I need examples where location names are mentioned with prepositions like "in" or "at"'
        - 'I need examples with entities appearing in quotation marks or parentheses'
        - 'I need examples where multiple entity types appear close together'"""
    )

    syntactic_complexity: str = Field(
        description="""Describe the syntactic complexity level you need in examples to match the current text.
        Choose from these levels and explain your reasoning:
        - 'simple': Simple sentences with clear structure and shallow dependencies (1-2 levels)
        - 'medium': Moderate complexity with clauses but relatively clear structure (2-4 levels)  
        - 'complex': Complex long sentences with multi-level nesting and deep dependencies (4+ levels)

        Explain your choice, for example:
        - 'I need complex examples because this text has multiple nested clauses that make entity boundaries unclear'
        - 'I need simple examples because this text is straightforward and direct'"""
    )

    entity_density: str = Field(
        description="""Describe the entity density pattern you need in examples to match the current text.
        Choose from these density levels and explain:
        - 'sparse': Few entities, 0-1 entities per sentence, entities are far apart
        - 'medium': Moderate density, 1-3 entities per sentence, relatively even distribution
        - 'dense': High density, 3+ entities per sentence, entities appear close together

        Also describe entity boundary characteristics:
        - Are boundaries clear or ambiguous? Any nesting or overlap?
        - Are there compound entity names?
        - Is there diversity in entity types?

        Example: 'I need dense examples because entities are tightly packed with clear boundaries in this text'"""
    )
